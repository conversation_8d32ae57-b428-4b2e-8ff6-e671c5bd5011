import etcd3
import time

# --- 1. 连接到 etcd ---
try:
    # 默认连接到 localhost:2379
    client = etcd3.client(host='*************', port=30379)
    # 检查一下连接是否成功
    client.status()
    print("✅ 成功连接到 etcd！")
except Exception as e:
    print(f"❌ 连接失败: {e}")
    print("👉 请确保你的 etcd 服务正在运行。")
    exit()

# 定义我们要操作的键和值
MY_KEY = '/network/nodes/node-1'
INITIAL_VALUE = 'abc-123-def-456'
UPDATED_VALUE = 'xyz-987-uvw-654'


# --- 2. 写入 (Put) 数据 ---
print("\n--- 写入操作 ---")
client.put(MY_KEY, INITIAL_VALUE)
print(f"🔑 已写入: Key='{MY_KEY}', Value='{INITIAL_VALUE}'")


# --- 3. 读取 (Get) 数据 ---
# print("\n--- 读取操作 ---")
# value, metadata = client.get(MY_KEY)
# # 注意：etcd 返回的值是 bytes 类型, 需要解码成字符串
# if value is not None:
#     print(f"🔍 读取到: Value='{value.decode('utf-8')}'")
#     # metadata 包含很多有用的信息，比如版本号
#     print(f"  (数据版本: {metadata.mod_revision})")


# --- 4. 监视 (Watch) 数据变化 ---
# Watch 是一个持续的过程，我们这里用一个简单的循环来演示
# 在真实应用中，你可能会把它放在一个后台线程里
print("\n--- 监视操作 ---")
print(f"👀 正在监视 Key '{MY_KEY}' 的变化... (这是一个演示，10秒后会自动结束)")
print("✨ 你可以现在打开另一个终端，用下面的命令来修改它，看看会发生什么：")
print(f"docker exec my-etcd etcdctl put {MY_KEY} {UPDATED_VALUE}")

try:
    # 创建一个监视器，设置一个10秒的超时
    watch_id = client.add_watch_prefix_callback(MY_KEY, lambda event: event_handler(event))
    # events_iterator, cancel = client.watch(MY_KEY)
    # for event in events_iterator:
    #     print("\n💥 检测到变化！")
    #     print(f"  新的值是: '{event.value.decode('utf-8')}'")
    #     # 收到一次更新后，我们就取消监视，结束演示
    #     # cancel()
except Exception:
    print("\n监视超时或被取消。")

def event_handler(event):
    if event.event_type == 'PUT':
        print(f"💥 检测到变化！新值: '{event.value.decode('utf-8')}'")
    elif event.event_type == 'DELETE':
        print("🗑️ Key 已被删除！")


# --- 5. 删除 (Delete) 数据 ---
print("\n--- 删除操作 ---")
was_deleted = client.delete(MY_KEY)
if was_deleted:
    print(f"🗑️ 成功删除 Key='{MY_KEY}'")

# 验证一下是否真的删除了
value, metadata = client.get(MY_KEY)
if value is None:
    print("✅ 验证成功，Key 已不复存在。")
